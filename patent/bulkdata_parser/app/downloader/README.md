# url_getter

## 概要

引数の URL のファイルをダウンロードする

s3 へのマルチパートダウンロードで 10GB 以上のファイルに対応する

特許バルクデータのダウンロード用のサービスだが、特許バルクデータのダウンロードに認証は必要ない（ページのアクセスには必要）ためヘッダーに認証は指定していない

## 前提

- python

※ 圧縮ファイル解凍の残骸があります。なるべく処理を python に寄せるために残していますが現状大きいサイズのファイルの解凍に対応できていないため無視してください

## 環境構築

```
make setup
```

## ローカルコマンド実行

### コマンド

```
make local.downloder [ダウンロードファイルのURL] [ダウンロードするバルクデータの識別子]
```

### 実行例

```
make local.downloder
```

### ダウンロードするバルクデータの識別子

- (日本)公開特許 PDF((Japan)PDF(Patent_Released)) ・・・ `public_patent_pdf`
- 出願マスタ（特実）(Weekly_Update_Data_Appm_PatentUtility) ・・・ `application_master`
- (日本)公開特許PDF((Japan)PDF(Patent_Released)) ・・・ `pct_jpo_patent_pdf`

## ローカル PostgreSQL でのインポート処理確認

### 1. ローカル PostgreSQL の起動

```sh
# Docker Composeを使用してPostgreSQLを起動
# パスワードは postgres
make local.run_postgres
```

### 2. インポート

pdf_parserで生成したローカルCSVファイルを直接PostgreSQLにインポートします。

```sh
# PostgreSQLに直接インポート、パスは絶対パスで指定
CSV_FILE_PATH="../app/pdf_parser/local_output/pct_jpo_patent_pdf_20250521.csv"
CSV_FILE_PATH=${CSV_FILE_PATH} make local.import_patents_db
```

### 3. データの確認

```sh
# テーブル内のデータを確認
psql -h localhost -p 5432 -U postgres -d patent -c "SELECT * FROM patents LIMIT 10;" | less -S
```

このスクリプトは、S3上の `${KIND}_parse_result_csv/${TARGET_DATE}/` パス配下のCSVファイルを検索し、インポートします。

## ローカル PostgreSQL でのエクスポート処理確認

ローカルPostgreSQLを参照して、parquet ファイルを作成します。

```sh
TARGET_DATE=19991231 make local.export_parquet
```
