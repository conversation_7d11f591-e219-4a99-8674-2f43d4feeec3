import os
import sys
import traceback
from pathlib import Path

import pandas as pd
import psycopg2
from psycopg2 import sql
from psycopg2.extras import execute_values

# プロジェクトのルートディレクトリをパスに追加
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# CSVファイルのカラム名を定義
columns = [
    "pdf_url",
    "issue",
    "publication_number",
    "abstract",
    "solution",
    "claim",
    "claims",
    "claim_1",
    "applicant",
    "applicant_name",
    "applicant_address",
    "application_number",
    "publish_date",
    "filing_date",
    "inventor_name",
    "invention_title",
    "inventor_address",
    "image_urls",
    "image_url",
    "ipc",
    "fi",
    "fterms",
    "is_entertainment",
    "registration_number",
    "registration_date",
    "summary",
    "ulid",
    "lang",
    "version",
    "created_at",
    "stockmark_published_at",
    "application_number_display",
    "publication_number_display",
    "registration_number_display",
    "pct_application_number",
    "pct_publication_number",
    "pct_publication_date",
    "priority_numbers",
    "priority_dates",
    "priority_countries",
    "publication_number_of_japanese_translation_of_pct_application",
    "technical_field",
    "background_art",
    "citation_list_patent",
    "citation_list_non_patent",
    "summary_of_invention_issue",
    "summary_of_invention_solution",
    "summary_of_invention_effect",
    "description_of_drawings",
    "description_of_embodiments",
    "industrial_applicability",
]


def get_create_stage_table_query():
    """テーブル作成SQLを取得する"""
    sql_path = project_root / "database" / "aurora" / "create_patents_stage_table.sql"
    with open(sql_path, "r") as f:
        return f.read().format()


def get_db_connection():
    """データベース接続を取得する"""
    db_host = os.environ.get("DB_HOST", "localhost")
    db_port = os.environ.get("DB_PORT", "5432")
    db_user = os.environ.get("DB_USER", "postgres")
    db_password = os.environ.get("DB_PASSWORD", "postgres")
    db_name = os.environ.get("DB_NAME", "patent")

    return psycopg2.connect(
        host=db_host,
        port=db_port,
        user=db_user,
        password=db_password,
        database=db_name,
    )


def main():
    """
    bulkdata_parser用の処理を実行する
    1. ローカルホストのPostgreSQLに接続
    2. CSVをpatentsテーブルに直接インポート
    """
    try:
        # 環境変数からCSVファイルパスを取得
        csv_file_path = os.environ.get("CSV_FILE_PATH")
        if not csv_file_path:
            print("CSV_FILE_PATH 環境変数が設定されていません")
            return False

        if not os.path.exists(csv_file_path):
            print(f"CSVファイルが見つかりません: {csv_file_path}")
            return False

        print(f"Importing CSV from: {csv_file_path}")

        # データベースに接続
        conn = get_db_connection()
        print("データベースに接続しました")

        try:
            # 1. patentsテーブルが存在することを確認
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'patents')"
                )
                table_exists = cur.fetchone()[0]
                if not table_exists:
                    raise Exception("patents テーブルが存在しません")
                print("patentsテーブル存在確認: OK")

            # 2. CSVファイルを読み込み、適切な形式に変換
            df = pd.read_csv(csv_file_path, header=None, names=columns)
            print(f"CSVファイルを読み込みました。行数: {len(df)}")

            # 3. COPYコマンドでCSVをpatentsテーブルに直接インポート
            print("CSVインポートを開始します...")

            with conn.cursor() as cur:
                with open(csv_file_path, "r", encoding="utf-8") as f:
                    copy_sql = f"""
                    COPY patents ({','.join(columns)}) 
                    FROM STDIN 
                    WITH (
                        FORMAT CSV,
                        HEADER false,
                        NULL '',
                        DELIMITER ','
                    )
                    """
                    cur.copy_expert(copy_sql, f)

            conn.commit()
            print("CSVのインポートに成功しました")

            # インポート後の行数を確認
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM patents")
                count = cur.fetchone()[0]
                print(f"patentsテーブルの総行数: {count}")

        except Exception as e:
            conn.rollback()
            print(f"処理中のエラー: {e}")
            print(traceback.format_exc())
            raise e
        finally:
            conn.close()
            print("データベース接続を閉じました")

        print("bulkdata_parser用CSVのpatentsテーブルへの直接インポート処理が完了しました")
        return True

    except Exception as e:
        print(f"エラーが発生しました: {e}")
        print(traceback.format_exc())
        return False


if __name__ == "__main__":
    main()
