ENV ?= development

AWS_LAMBDA_FUNCTION_NAMES := \
	patent-bulkdata-parser-output-add-id-patents-$(ENV) \
	patent-bulkdata-parser-import-patents-db-$(ENV) \
	patent-bulkdata-parser-downloader-$(ENV) \
	patent-bulkdata-parser-copy-list-object-$(ENV) \
	patent-bulkdata-parser-get-under-prefix-list-$(ENV)

include ${CURDIR}/../../tools/mk-lib/formatter.poetry.mk
include ${CURDIR}/../../tools/mk-lib/deployer.mk

.PHONY: local.*
local.downloder:
	PYTHONPATH=. \
	BUCKET_NAME=discovery-patent \
	AWS_PROFILE=saml \
	poetry run python scripts/local/run_downloader.py ${URL} ${KIND}

local.zip_file_decompression:
	PYTHONPATH=. \
	BUCKET_NAME=patent-bulkdata-development \
	AWS_PROFILE=saml \
	poetry run python scripts/local/run_zip_file_decompression.py ${KEY} ${TARGET_DATE} ${KIND}

local.targz_file_decompression:
	PYTHONPATH=. \
	BUCKET_NAME=patent-bulkdata-development \
	AWS_PROFILE=saml \
	poetry run python scripts/local/run_targz_file_decompression.py ${KEY} ${TARGET_DATE} ${KIND}

local.copy_list_object:
	PYTHONPATH=. \
	BUCKET_NAME=patent-bulkdata-development \
	PUBLIC_BUCKET_NAME=patent-public-resourse-development \
	AWS_PROFILE=saml \
	poetry run python scripts/local/run_copy_list_object.py ${SOURCE_PREFIX} ${TARGET_PREFIX} ${SOURCE_SUFFIX}

local.get_under_prefix_list:
	PYTHONPATH=. \
	BUCKET_NAME=patent-bulkdata-development \
	AWS_PROFILE=saml \
	poetry run python scripts/local/run_get_under_prefix_list.py ${SOURCE_PREFIX}

local.run_postgres:
	docker compose -f ./docker/local-postgres/docker-compose.yml up -d

local.import_patents_db:
	IS_LOCAL=true \
	poetry run python scripts/local/import_csv_to_postgres.py

local.export_patents_db:
	IS_LOCAL=true \
	poetry run python scripts/local/run_output_add_id_patents.py ${TARGET_DATE}

.PHONY: deploy
deploy: docker/push update-lambda-function

deploy_ecs: docker/push

.PHONY: connect-patents-db
connect-patents-db:
	./scripts/connect-patents-db.sh
