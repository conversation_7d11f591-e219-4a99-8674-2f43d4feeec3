import os
from typing import List

import pandas as pd
import psycopg2
import sqlalchemy as sa


def exec(sql: str) -> pd.DataFrame:
    engine = __get_client()
    return pd.read_sql(sql, con=engine)


def exec_transaction(sql_list: List[str]):
    with __get_connection() as conn:
        with conn.cursor() as cur:
            for sql in sql_list:
                print(sql)
                cur.execute(sql)


def __get_client():
    user = os.environ.get("DB_HOST", "postgres")
    password = os.environ.get("DB_PASSWORD", "postgres")
    host = os.environ.get("DB_HOST", "localhost")
    port = os.environ.get("DB_PORT", "5432")
    database = os.environ.get("DB_NAME", "patent")

    url = "postgresql://{}:{}@{}:{}/{}".format(user, password, host, port, database)

    return sa.create_engine(url, echo=False)


def __get_connection():
    user = os.environ.get("DB_HOST", "postgres")
    password = os.environ.get("DB_PASSWORD", "postgres")
    host = os.environ.get("DB_HOST", "localhost")
    port = os.environ.get("DB_PORT", "5432")
    database = os.environ.get("DB_NAME", "patent")

    return psycopg2.connect(
        user=user, password=password, host=host, port=port, database=database
    )
