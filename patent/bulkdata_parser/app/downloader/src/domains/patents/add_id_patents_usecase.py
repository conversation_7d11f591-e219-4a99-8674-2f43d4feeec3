from logging import getLogger
from typing import List

import pandas as pd

from src.domains.const import PatentKind
from src.handler import local_postgres_handler, postgres_handler, s3_handler

logger = getLogger(__name__)


def import_new_version(
    version: str,
    kind: PatentKind,
) -> List[str]:
    bucket_name = s3_handler.get_bucket_name()
    keys = __get_new_version_csv_keys(version, kind)

    sql_list = __get_import_sql_list(bucket_name, keys)

    postgres_handler.exec_transaction(sql_list)

    return keys


def select(
    version: str, language: str, id: int, limit: int, is_local: bool = False
) -> pd.DataFrame:
    sql = __get_select_sql(version, language, id, limit)

    if is_local:
        return local_postgres_handler.exec(sql)
    else:
        return postgres_handler.exec(sql)


def save_bucket(item: pd.DataFrame, key: str):
    s3_handler.save_parquet(item, key)


def __get_import_sql_list(bucket_name: str, keys: List[str]):
    sql_list = []

    sql_list.append(__get_create_stage_table_query())

    for key in keys:
        sql_list.append(__get_import_by_s3_sql(bucket_name, key))

    sql_list.append(__get_insert_sql())

    return sql_list


def __get_new_version_csv_keys(
    version: str,
    kind: PatentKind,
):
    prefix = f"{kind}_parse_result_csv/{version}/"
    content_list = s3_handler.get_content_list_by_prefix_under(prefix)

    keys = []

    for content in content_list:
        if "_SUCCESS" in content:
            continue

        keys.append(content)

    return keys


def __get_select_sql(version: str, dataset_type: str, id: int, limit: int):
    with open("./database/aurora/select_add_id_patents.sql") as f:
        tmp_sql = f.read()

        return tmp_sql.format(version, dataset_type, id, limit)


def __get_import_by_s3_sql(bucket_name: str, key: str):
    with open("./database/aurora/import_patents_from_s3.sql") as f:
        tmp_sql = f.read()

        return tmp_sql.format(bucket_name, key)


def __get_create_stage_table_query():
    with open("./database/aurora/create_patents_stage_table.sql") as f:
        tmp_sql = f.read()

        return tmp_sql.format()


def __get_insert_sql():
    with open("./database/aurora/insert_patents_from_tmp.sql") as f:
        tmp_sql = f.read()

        return tmp_sql.format()
