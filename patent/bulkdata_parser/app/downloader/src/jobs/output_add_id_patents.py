import json
import os
from dataclasses import asdict, dataclass, field
from typing import List, Optional, Tuple

import pandas as pd
import pyarrow as pa
from pyarrow import parquet as pq

from src.domains.patents import add_id_patents_usecase
from src.handler import step_functions_handler

RDB_SELECT_LIMIT_COUNT = 6400
PARQUET_OUTPUT_LIMIT_COUNT = 800

# ローカル用
is_local: bool = os.environ.get("IS_LOCAL", "False").lower() == "true"
local_pdf_path = os.environ.get("PDF_PATH", "")


@dataclass
class Result:
    ja_files: List[str] = field(default_factory=list)
    en_files: List[str] = field(default_factory=list)
    ja_count: int = 0
    en_count: int = 0


def transform_to_struct(df: pd.DataFrame) -> pd.DataFrame:
    """
    OpenSearch が辞書型として扱う項目を変換
    """

    def is_valid_value(value):
        import numpy as np

        try:
            # 配列やリストの場合
            if hasattr(value, "__len__") and not isinstance(value, str):
                return len(value) > 0

            # 通常の値の場合
            return not pd.isna(value) and (
                not isinstance(value, str) or value.strip() != ""
            )
        except (ValueError, TypeError):
            # pd.isna()でエラーが発生した場合（空の配列など）
            return hasattr(value, "__len__") and len(value) > 0

    # citation_list の構造化
    citation_list_cols = [
        "citation_list_patent",
        "citation_list_non_patent",
    ]
    if all(col in df.columns for col in citation_list_cols):
        df["citation_list"] = df.apply(
            lambda row: {
                "patent": row["citation_list_patent"]
                if is_valid_value(row["citation_list_patent"])
                else [],
                "non_patent": row["citation_list_non_patent"]
                if is_valid_value(row["citation_list_non_patent"])
                else [],
            },
            axis=1,
        )
        df = df.drop(["citation_list_patent", "citation_list_non_patent"], axis=1)

    # summary_of_invention の構造化
    summary_of_invention_cols = [
        "summary_of_invention_issue",
        "summary_of_invention_solution",
        "summary_of_invention_effect",
    ]
    if all(col in df.columns for col in summary_of_invention_cols):
        df["summary_of_invention"] = df.apply(
            lambda row: {
                "issue": row["summary_of_invention_issue"]
                if is_valid_value(row["summary_of_invention_issue"])
                else [],
                "solution": row["summary_of_invention_solution"]
                if is_valid_value(row["summary_of_invention_solution"])
                else [],
                "effect": row["summary_of_invention_effect"]
                if is_valid_value(row["summary_of_invention_effect"])
                else [],
            },
            axis=1,
        )
        df = df.drop(summary_of_invention_cols, axis=1)

    return df


def exec(version: str) -> Optional[Result]:
    try:
        ja_files, ja_count = output(version, "ja")
        en_files, en_count = output(version, "en")

        if is_local:
            print("ja_files:", ja_files)
            print("en_files:", en_files)
            return Result(ja_files, en_files, ja_count, en_count)

        result = {
            "RESULT": True,
            "JA_COUNT": ja_count,
            "EN_COUNT": en_count,
            "JA_FILES": ja_files,
            "EN_FILES": en_files,
            "TARGET_DATE": version,
        }
        step_functions_handler.send_task_success(result)
        return Result(ja_files, en_files, ja_count, en_count)
    except Exception as e:
        step_functions_handler.send_task_failure(e, "E001")
        raise e


def output(version: str, language: str) -> Tuple[List[str], int]:
    num = 0
    count = 0
    result = []
    id = 0
    while True:
        print("id:", id, flush=True)
        df = add_id_patents_usecase.select(
            version, language, id, RDB_SELECT_LIMIT_COUNT, is_local
        )
        if len(df) == 0:
            return result, count

        id = df["id"].iloc[-1]

        # 一部のカラムの変換を追加（OpenSearch向け）
        df = transform_to_struct(df)

        count += len(df)
        df_list = [
            df[idx : idx + PARQUET_OUTPUT_LIMIT_COUNT]
            for idx in range(0, len(df), PARQUET_OUTPUT_LIMIT_COUNT)
        ]
        result.extend(df_list_output(df_list, version, language, num))
        num += 1


def df_list_output(
    df_list: List[pd.DataFrame], version: str, language: str, num: int
) -> List[str]:
    result = []
    for idx, item in enumerate(df_list):
        key = f"add_id_patents/{version}/{language}/{version}_{language}_patents-{str(num)}-{str(idx)}.parquet"

        if is_local:
            local_output_path = f"./local_output/{key}"
            os.makedirs(os.path.dirname(local_output_path), exist_ok=True)
            item.to_parquet(local_output_path, index=False)
            print(f"Saving parquet to local path: {local_output_path}")
        else:
            add_id_patents_usecase.save_bucket(item, key)

        result.append(key)

    return result
