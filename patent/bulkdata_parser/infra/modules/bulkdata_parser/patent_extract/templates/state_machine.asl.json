{"Comment": "A description of my state machine", "StartAt": "IsExsistTargetDate", "States": {"IsExsistTargetDate": {"Type": "Choice", "Choices": [{"Variable": "$.TARGET_DATE", "IsPresent": true, "Next": "SlackNotifyStart"}], "Default": "AddDefaultTargetDate"}, "AddDefaultTargetDate": {"Type": "Pass", "Next": "SlackNotifyStart", "Parameters": {"TARGET_DATE": ""}}, "SlackNotifyStart": {"Type": "Task", "Comment": "開始通知", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${slack_notifier_arn}:$LATEST", "Payload": {"MESSAGE.$": "States.Format('特許の取得処理を開始しました。 {} [{}]', $.TARGET_DATE, $$.Execution.Name)"}}, "ResultPath": null, "Next": "Parallel Download"}, "Parallel Download": {"Type": "<PERSON><PERSON><PERSON>", "Branches": [{"StartAt": "Registration PDF Download Add Parameter", "States": {"Registration PDF Download Add Parameter": {"Type": "Pass", "Parameters": {"KIND": "registration_patent_pdf", "TARGET_DATE.$": "$.TARGET_DATE"}, "Next": "Registration PDF Download Url Getter Lambda Invoke"}, "Registration PDF Download Url Getter Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "${url_getter_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Calc Target DateBy Registration PDF File URL"}, "Calc Target DateBy Registration PDF File URL": {"Type": "Pass", "Next": "Registration PDF File ListObjectsV2", "Parameters": {"DOWNLOAD_URL.$": "$.DOWNLOAD_URL", "KIND.$": "$.KIND", "TARGET_DATE.$": "States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-3))"}}, "Registration PDF File ListObjectsV2": {"Type": "Task", "Next": "Is Registration PDF File Exists", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file/registration_patent_pdf/{}', $.TARGET_DATE)", "MaxKeys": 100}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.Payload"}, "Is Registration PDF File Exists": {"Type": "Choice", "Choices": [{"Variable": "$.Payload.Contents", "IsPresent": true, "Next": "Registration PDF File Exists"}], "Default": "Registration PDF Download Run Task"}, "Registration PDF File Exists": {"Type": "Pass", "End": true}, "Registration PDF Download Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_downloader.py', $.DOWNLOAD_URL, $.KIND)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "Next": "Registration PDF File Tar Gunzip Add Parameter"}, "Registration PDF File Tar Gunzip Add Parameter": {"Type": "Pass", "Parameters": {"KEY.$": "States.Format('downloads/{}/{}/{}', $.KIND, $.TARGET_DATE, States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-1)))", "TARGET_DATE.$": "$.TARGET_DATE", "KIND.$": "$.KIND", "SAVE_PREFIX": "decompression_file_zip"}, "Next": "Registration PDF File Tar Gunzip RunTask"}, "Registration PDF File Tar Gunzip RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "Next": "Registration PDF File ListObjects"}, "Registration PDF File ListObjects": {"Type": "Task", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file_zip/registration_patent_pdf/{}', $.TARGET_DATE)"}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.Payload", "Next": "Registration PDF File Unzip Map"}, "Registration PDF File Unzip Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Registration PDF File Unzip  RunTask", "States": {"Registration PDF File Unzip  RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "End": true}}}, "ItemsPath": "$.Payload.Contents", "ItemSelector": {"KEY.$": "$$.Map.Item.Value.Key", "TARGET_DATE.$": "$.TARGET_DATE", "KIND.$": "$.KIND", "SAVE_PREFIX": "decompression_file"}, "ResultPath": "$.Payload", "Next": "Registration PDF Create Copy Prefix"}, "Registration PDF Create Copy Prefix": {"Type": "Pass", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "SOURCE_PREFIX.$": "States.Format('decompression_file/registration_patent_pdf/{}/DOCUMENT/P_B1/', $.TARGET_DATE)", "TARGET_PREFIX.$": "States.Format('patent/registration_patent_pdf/{}/', $.TARGET_DATE)"}, "Next": "Registration PDF File Copy To Public Bucket Invoke"}, "Registration PDF File Copy To Public Bucket Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload.$": "$", "FunctionName": "${copy_list_object_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true, "ResultPath": "$.Payload"}}}, {"StartAt": "Publication PDF Download Add Parameter", "States": {"Publication PDF Download Add Parameter": {"Type": "Pass", "Next": "Publication PDF Download Url Getter Lambda Invoke", "Parameters": {"KIND": "public_patent_pdf", "TARGET_DATE.$": "$.TARGET_DATE"}}, "Publication PDF Download Url Getter Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "${url_getter_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Calc Target DateBy Publication PDF File URL"}, "Calc Target DateBy Publication PDF File URL": {"Type": "Pass", "Next": "Publication PDF File ListObjectsV2", "Parameters": {"DOWNLOAD_URL.$": "$.DOWNLOAD_URL", "KIND.$": "$.KIND", "TARGET_DATE.$": "States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-3))"}}, "Publication PDF File ListObjectsV2": {"Type": "Task", "Next": "Is Publication PDF File Exists", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file/public_patent_pdf/{}/', $.TARGET_DATE)", "MaxKeys": 100}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.PublicationPayload"}, "PCT_JPO PDF File ListObjectsV2": {"Type": "Task", "Next": "Is Both PDF File Exists", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file/pct_jpo_patent_pdf/{}/', $.TARGET_DATE)", "MaxKeys": 100}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.PCTJpoPayload"}, "Is Both PDF File Exists": {"Type": "Choice", "Choices": [{"And": [{"Variable": "$.PublicationPayload.Contents", "IsPresent": true}, {"Variable": "$.PCTJpoPayload.Contents", "IsPresent": true}], "Next": "Both PDF File Exists"}], "Default": "Publication PDF Download Run Task"}, "Publication PDF and PCT_JPO PDF File Exists": {"Type": "Pass", "End": true}, "Publication PDF Download Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_downloader.py', $.DOWNLOAD_URL, $.KIND)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "Next": "Publication PDF File Tar Gunzip Add Parameter"}, "Publication PDF File Tar Gunzip Add Parameter": {"Type": "Pass", "Parameters": {"KEY.$": "States.Format('downloads/{}/{}/{}', $.KIND, $.TARGET_DATE, States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-1)))", "TARGET_DATE.$": "$.TARGET_DATE", "KIND.$": "$.KIND", "SAVE_PREFIX": "decompression_file_zip"}, "Next": "Publication PDF File Tar Gunzip RunTask"}, "Publication PDF File Tar Gunzip RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "Next": "Publication PDF Parallel Copy", "ResultPath": "$.Payload"}, "Publication PDF Parallel Copy": {"Type": "<PERSON><PERSON><PERSON>", "Branches": [{"StartAt": "Publication PDF File ListObjects", "States": {"Publication PDF File ListObjects": {"Type": "Task", "Next": "PCT_JPO PDF File ListObjects", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file_zip/public_patent_pdf/{}', $.TARGET_DATE)"}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.Payload"}, "Publication PDF File Unzip Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Publication PDF File Unzip  RunTask", "States": {"Publication PDF File Unzip  RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "End": true}}}, "ItemsPath": "$.Payload.Contents", "ItemSelector": {"KEY.$": "$$.Map.Item.Value.Key", "TARGET_DATE.$": "$.TARGET_DATE", "KIND.$": "$.KIND", "SAVE_PREFIX": "decompression_file"}, "ResultPath": "$.Payload", "Next": "Publication Patent PDF Create Copy Prefix"}, "Publication Patent PDF Create Copy Prefix": {"Type": "Pass", "Next": "Publication PDF File Copy To Public Bucket Invoke", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "SOURCE_PREFIX.$": "States.Format('decompression_file/public_patent_pdf/{}/DOCUMENT/P_A1/', $.TARGET_DATE)", "TARGET_PREFIX.$": "States.Format('patent/public_patent_pdf/{}/', $.TARGET_DATE)"}}, "Publication PDF File Copy To Public Bucket Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload.$": "$", "FunctionName": "${copy_list_object_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true, "ResultPath": "$.Payload"}}}, {"StartAt": "PCT_JPO PDF File ListObjects", "States": {"PCT_JPO PDF File ListObjects": {"Type": "Task", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file_zip/pct_jpo_patent_pdf/{}', $.TARGET_DATE)"}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.Payload", "Next": "PCT_JPO PDF File Unzip Map"}, "PCT_JPO PDF File Unzip Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "PCT_JPO PDF File Unzip RunTask", "States": {"PCT_JPO PDF File Unzip RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "End": true}}}, "ItemsPath": "$.PCTJPOPayload.Contents", "ItemSelector": {"KEY.$": "$.Map.Item.Value.Key", "TARGET_DATE.$": "$.TARGET_DATE", "KIND": "pct_jpo_patent_pdf", "SAVE_PREFIX": "decompression_file"}, "ResultPath": "$.PCTJPOUnzipResult", "Next": "PCT_JPO PDF Create Copy Prefix"}, "PCT_JPO PDF Create Copy Prefix": {"Type": "Pass", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "SOURCE_PREFIX.$": "States.Format('decompression_file/pct_jpo_patent_pdf/{}/DOCUMENT/P_P1/', $.TARGET_DATE)", "TARGET_PREFIX.$": "States.Format('patent/pct_jpo_patent_pdf/{}/', $.TARGET_DATE)"}, "Next": "PCT_JPO PDF File Copy To Public Bucket Invoke"}, "PCT_JPO PDF File Copy To Public Bucket Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload.$": "$", "FunctionName": "${copy_list_object_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true, "ResultPath": "$.Payload"}}}], "End": true}}}, {"StartAt": "Application Master Download Add Parameter", "States": {"Application Master Download Add Parameter": {"Type": "Pass", "Next": "Application Master Download Url Getter Lambda Invoke", "Parameters": {"KIND": "application_master", "TARGET_DATE.$": "$.TARGET_DATE"}}, "Application Master Download Url Getter Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "${url_getter_arn}:$LATEST"}, "Retry": [{"ErrorEquals": ["States.ALL"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Calc TargetDate By Application Master File URL"}, "Calc TargetDate By Application Master File URL": {"Type": "Pass", "Next": "Application Master File ListObjectsV2", "Parameters": {"DOWNLOAD_URL.$": "$.DOWNLOAD_URL", "KIND.$": "$.KIND", "TARGET_DATE.$": "States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-3))"}}, "Application Master File ListObjectsV2": {"Type": "Task", "Next": "Is Application Master File Exists", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('decompression_file/application_master/{}', $.TARGET_DATE)", "MaxKeys": 100}, "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "ResultPath": "$.Payload"}, "Is Application Master File Exists": {"Type": "Choice", "Choices": [{"Variable": "$.Payload.Contents", "IsPresent": true, "Next": "Application Master File Exists"}], "Default": "Application Master Download Run Task"}, "Application Master File Exists": {"Type": "Pass", "End": true}, "Application Master Download Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_downloader.py', $.DOWNLOAD_URL, $.KIND)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "Next": "Application Master File Tar Gunzip Add Parameter"}, "Application Master File Tar Gunzip Add Parameter": {"Type": "Pass", "Parameters": {"KEY.$": "States.Format('downloads/{}/{}/{}', $.KIND, $.TARGET_DATE, States.ArrayGetItem(States.StringSplit($.DOWNLOAD_URL, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($.DOWNLOAD_URL, '/')),-1)))", "TARGET_DATE.$": "$.TARGET_DATE", "KIND.$": "$.KIND", "SAVE_PREFIX": "decompression_file"}, "Next": "Application Master File Tar Gunzip RunTask"}, "Application Master File Tar Gunzip RunTask": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.sync", "Parameters": {"LaunchType": "FARGATE", "Cluster": "${unzip_cluster_arn}", "TaskDefinition": "${unzip_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${unzip_subnet_id}"], "SecurityGroups": ["${unzip_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-unzip-${environment}", "Command.$": "States.Array('node', 'dest/scripts/unzip.js', $.TARGET_DATE,$.KIND,$.KEY, $.SAVE_PREFIX)"}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "End": true, "ResultPath": "$.Payload"}}}], "Next": "Parallel Result Filter Target Object"}, "Parallel Result Filter Target Object": {"Type": "Pass", "Next": "Target Object Filter Target Date", "Parameters": {"TARGET.$": "States.ArrayGetItem($, 0)"}}, "Target Object Filter Target Date": {"Type": "Pass", "Next": "<PERSON><PERSON><PERSON>", "Parameters": {"TARGET_DATE.$": "$.TARGET.TARGET_DATE", "SAVE_PREFIX": "s3://${patent_bulkdata_bucket_name}"}}, "Parallel": {"Type": "<PERSON><PERSON><PERSON>", "Next": "Output Patent Lambda Invoke", "Branches": [{"StartAt": "Public Parse PDF And Merge Application Master StartJobRun", "States": {"Public Parse PDF And Merge Application Master StartJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "ClientToken.$": "States.Format('{}{}', States.UUID(), 'publicpatent')", "Name": "public-pdf-parse", "ExecutionRoleArn": "${pdf_parser_role_arn}", "JobDriver": {"SparkSubmit": {"EntryPoint": "${entry_point}", "EntryPointArguments.$": "States.Array($.TARGET_DATE, 's3://${patent_bulkdata_bucket_name}', 'public_patent_pdf')", "SparkSubmitParameters": "--conf spark.emr-serverless.driverEnv.PYSPARK_DRIVER_PYTHON=/usr/local/bin/python3.10 --conf spark.emr-serverless.driverEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.executorEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.executorEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.emr-serverless.driverEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name}"}}}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:startJobRun", "ResultPath": "$.Output", "Next": "Public Patent Parameter Convert"}, "Public Patent Parameter Convert": {"Type": "Pass", "Parameters": {"SAVE_PREFIX.$": "$.SAVE_PREFIX", "TARGET_DATE.$": "$.TARGET_DATE", "JobRunId.$": "$.Output.JobRunId"}, "Next": "Public Patent Default Wait"}, "Public Patent Default Wait": {"Type": "Wait", "Seconds": 900, "Next": "Public Patent Parse PDF And Merge Application Master Job Wait"}, "Public Patent Parse PDF And Merge Application Master Job Wait": {"Type": "Wait", "Seconds": 300, "Next": "Public Patent Parse PDF And Merge Application Master GetJobRun"}, "Public Patent Parse PDF And Merge Application Master GetJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "JobRunId.$": "$.JobRunId"}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:getJobRun", "ResultPath": "$.Output", "Next": "Is Sucsess Public Patent Parse PDF Choice"}, "Is Sucsess Public Patent Parse PDF Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Output.JobRun.State", "StringEquals": "SUCCESS", "Next": "Add Public Patent Parameter"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "FAILED", "Next": "Public Patent Fail"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "CANCEL", "Next": "Public Patent Cancel"}], "Default": "Public Patent Parse PDF And Merge Application Master Job Wait"}, "Add Public Patent Parameter": {"Type": "Pass", "Next": "Import Public Patent Run Task", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "KIND": "public_patent_pdf"}}, "Import Public Patent Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.waitForTaskToken", "HeartbeatSeconds": 600, "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_import_patents_db.py', $.TARGET_DATE, $.KIND)", "Environment": [{"Name": "TASK_TOKEN", "Value.$": "$$.Task.Token"}]}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "End": true}, "Public Patent Fail": {"Type": "Fail"}, "Public Patent Cancel": {"Type": "Fail"}}}, {"StartAt": "PCT_JPO PDF StarterWait", "States": {"PCT_JPO PDF StarterWait": {"Type": "Wait", "Seconds": 20, "Next": "PCT_JPO PDF StartJobRun"}, "PCT_JPO PDF StartJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "ClientToken.$": "States.Format('{}{}', States.UUID(), 'pctjpopatent')", "Name": "pct-jpo-pdf-parse", "ExecutionRoleArn": "${pdf_parser_role_arn}", "JobDriver": {"SparkSubmit": {"EntryPoint": "${entry_point}", "EntryPointArguments.$": "States.Array($.TARGET_DATE, 's3://${patent_bulkdata_bucket_name}', 'pct_jpo_patent_pdf')", "SparkSubmitParameters": "--conf spark.emr-serverless.driverEnv.PYSPARK_DRIVER_PYTHON=/usr/local/bin/python3.10 --conf spark.emr-serverless.driverEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.executorEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.executorEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.emr-serverless.driverEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name}"}}}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:startJobRun", "ResultPath": "$.Output", "Next": "PCT_JPO Parameter Convert"}, "PCT_JPO Parameter Convert": {"Type": "Pass", "Parameters": {"SAVE_PREFIX.$": "$.SAVE_PREFIX", "TARGET_DATE.$": "$.TARGET_DATE", "JobRunId.$": "$.Output.JobRunId"}, "Next": "PCT_JP<PERSON> Default Wait"}, "PCT_JPO Default Wait": {"Type": "Wait", "Seconds": 900, "Next": "PCT_JPO PDF Job Wait"}, "PCT_JPO PDF Job Wait": {"Type": "Wait", "Seconds": 300, "Next": "PCT_JPO PDF GetJobRun"}, "PCT_JPO PDF GetJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "JobRunId.$": "$.JobRunId"}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:getJobRun", "ResultPath": "$.Output", "Next": "Is Sucsess PCT_JPO PDF Choice"}, "Is Sucsess PCT_JPO PDF Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Output.JobRun.State", "StringEquals": "SUCCESS", "Next": "Add PCT_JPO Parameter"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "FAILED", "Next": "PCT_JPO Fail"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "CANCEL", "Next": "PCT_JPO Cancel"}], "Default": "PCT_JPO PDF Job Wait"}, "Add PCT_JPO Parameter": {"Type": "Pass", "Next": "Import PCT_JPO Run Task", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "KIND": "pct_jpo_patent_pdf"}}, "Import PCT_JPO Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.waitForTaskToken", "HeartbeatSeconds": 600, "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_import_patents_db.py', $.TARGET_DATE, $.KIND)", "Environment": [{"Name": "TASK_TOKEN", "Value.$": "$$.Task.Token"}]}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "End": true}, "PCT_JPO Fail": {"Type": "Fail"}, "PCT_JPO Cancel": {"Type": "Fail"}}}, {"StartAt": "Registration Patent Parse PDF And Merge Application Master StarterWait", "States": {"Registration Patent Parse PDF And Merge Application Master StarterWait": {"Type": "Wait", "Seconds": 40, "Next": "Registration Patent Parse PDF And Merge Application Master StartJobRun"}, "Registration Patent Parse PDF And Merge Application Master StartJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "ClientToken.$": "States.Format('{}{}', States.UUID(), 'registrationpatent')", "Name": "registration-pdf-parse", "ExecutionRoleArn": "${pdf_parser_role_arn}", "JobDriver": {"SparkSubmit": {"EntryPoint": "${entry_point}", "EntryPointArguments.$": "States.Array($.TARGET_DATE, 's3://${patent_bulkdata_bucket_name}', 'registration_patent_pdf')", "SparkSubmitParameters": "--conf spark.emr-serverless.driverEnv.PYSPARK_DRIVER_PYTHON=/usr/local/bin/python3.10 --conf spark.emr-serverless.driverEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PYSPARK_PYTHON=/usr/local/bin/python3.10 --conf spark.executorEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.executorEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.executorEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_PATENT_DOMAIN_NAME=${patent_public_resourse_domain_name} --conf spark.emr-serverless.driverEnv.BUCKET_NAME=${patent_bulkdata_bucket_name} --conf spark.emr-serverless.driverEnv.PUBLIC_BUCKET_NAME=${patent_public_resourse_bucket_name}"}}}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:startJobRun", "ResultPath": "$.Output", "Next": "Registration Patent Parameter Convert"}, "Registration Patent Parameter Convert": {"Type": "Pass", "Parameters": {"SAVE_PREFIX.$": "$.SAVE_PREFIX", "TARGET_DATE.$": "$.TARGET_DATE", "JobRunId.$": "$.Output.JobRunId"}, "Next": "Registration Patent Default Wait"}, "Registration Patent Default Wait": {"Type": "Wait", "Seconds": 900, "Next": "Registration Patent Parse PDF And Merge Application Master Job Wait (1)"}, "Registration Patent Parse PDF And Merge Application Master Job Wait (1)": {"Type": "Wait", "Seconds": 300, "Next": "Registration Patent Parse PDF And Merge Application Master GetJobRun"}, "Registration Patent Parse PDF And Merge Application Master GetJobRun": {"Type": "Task", "Parameters": {"ApplicationId": "${pdf_parser_application_id}", "JobRunId.$": "$.JobRunId"}, "Resource": "arn:aws:states:::aws-sdk:emrserverless:getJobRun", "ResultPath": "$.Output", "Next": "Is Sucsess Registration Patent Parse PDF Choice"}, "Is Sucsess Registration Patent Parse PDF Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Output.JobRun.State", "StringEquals": "SUCCESS", "Next": "Add Register Patent Parameter"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "FAILED", "Next": "Registration Patent Fail"}, {"Variable": "$.Output.JobRun.State", "StringEquals": "CANCEL", "Next": "Registration Patent Cancel"}], "Default": "Registration Patent Parse PDF And Merge Application Master Job Wait (1)"}, "Add Register Patent Parameter": {"Type": "Pass", "Next": "Import Registration Patent Run Task", "Parameters": {"TARGET_DATE.$": "$.TARGET_DATE", "KIND": "registration_patent_pdf"}}, "Import Registration Patent Run Task": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.waitForTaskToken", "HeartbeatSeconds": 600, "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_import_patents_db.py', $.TARGET_DATE, $.KIND)", "Environment": [{"Name": "TASK_TOKEN", "Value.$": "$$.Task.Token"}]}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "ResultPath": "$.Payload", "End": true}, "Registration Patent Cancel": {"Type": "Fail"}, "Registration Patent Fail": {"Type": "Fail"}}}], "ResultPath": "$.Payload"}, "Output Patent Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::ecs:runTask.waitForTaskToken", "HeartbeatSeconds": 600, "Parameters": {"LaunchType": "FARGATE", "Cluster": "${downloader_cluster_arn}", "TaskDefinition": "${downloader_task_definition_arn}", "NetworkConfiguration": {"AwsvpcConfiguration": {"Subnets": ["${downloader_subnet_id}"], "SecurityGroups": ["${downloader_security_group_id}"], "AssignPublicIp": "ENABLED"}}, "Overrides": {"ContainerOverrides": [{"Name": "patent-bulkdata-parser-downloader-${environment}", "Command.$": "States.Array('python', 'scripts/local/run_output_add_id_patents.py', $.TARGET_DATE)", "Environment": [{"Name": "TASK_TOKEN", "Value.$": "$$.Task.Token"}]}]}, "PropagateTags": "TASK_DEFINITION"}, "Retry": [{"ErrorEquals": ["ECS.AmazonECSException"], "IntervalSeconds": 30, "MaxAttempts": 3, "BackoffRate": 3}], "Next": "JapanesePatentsListObjects"}, "JapanesePatentsListObjects": {"Type": "Task", "Resource": "arn:aws:states:::aws-sdk:s3:listObjectsV2", "Parameters": {"Bucket": "${patent_bulkdata_bucket_name}", "Prefix.$": "States.Format('add_id_patents/{}/ja', $.TARGET_DATE)"}, "ResultPath": "$.Payload", "Next": "Is Running Job Choice"}, "Is Running Job Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Payload.Contents", "IsPresent": true, "Next": "JapanesePatentsCopyMap"}], "Default": "JapanesePapersNoContentPass"}, "JapanesePatentsCopyMap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "JapanesePatentsCopyToADP", "States": {"JapanesePatentsCopyToADP": {"Type": "Task", "Parameters": {"Bucket": "${adp_bucket_name}", "CopySource.$": "States.Format('${patent_bulkdata_bucket_name}/{}', $.Key)", "Key.$": "States.Format('basedata/auto/patent_ja/{}{}{}/{}', $.Year,$.Month,$.Day, $.FileName)"}, "Resource": "arn:aws:states:::aws-sdk:s3:copyObject", "End": true}}}, "ItemSelector": {"FileName.$": "States.ArrayGetItem(States.StringSplit($$.Map.Item.Value.Key, '/'), States.MathAdd(States.ArrayLength(States.StringSplit($$.Map.Item.Value.Key, '/')), -1))", "Key.$": "$$.Map.Item.Value.Key", "Year.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($$.State.EnteredTime, 'T'),0),'-'),0)", "Month.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($$.State.EnteredTime, 'T'),0),'-'),1)", "Day.$": "States.ArrayGetItem(States.StringSplit(States.ArrayGetItem(States.StringSplit($$.State.EnteredTime, 'T'),0),'-'),2)"}, "ItemsPath": "$.Payload.Contents", "ResultPath": "$.Payload", "Next": "SlackNotifyEnd"}, "SlackNotifyEnd": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"FunctionName": "${slack_notifier_arn}:$LATEST", "Payload": {"MESSAGE.$": "States.Format('特許の取得処理が完了しました。対象バージョン：{} 総数：{}件 英語：{}件 日本語：{}件 [{}]', $.TARGET_DATE, States.MathAdd($.EN_COUNT, $.JA_COUNT), $.EN_COUNT, $.JA_COUNT, $$.Execution.Name)"}}, "End": true}, "JapanesePapersNoContentPass": {"Type": "Pass", "Next": "No Content SlackNotifyEnd"}, "No Content SlackNotifyEnd": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "${slack_notifier_arn}:$LATEST", "Payload": {"MESSAGE": "特許の取得結果は0件でした"}}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 1, "MaxAttempts": 3, "BackoffRate": 2, "JitterStrategy": "FULL"}], "End": true}}}